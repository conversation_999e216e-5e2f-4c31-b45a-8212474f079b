import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TitleCase<PERSON>ipe } from '@angular/common';
import { Component } from '@angular/core';
import { ApisService } from '../../Services/apis.service';
import { FormsModule, NgModel } from '@angular/forms';

@Component({
  selector: 'app-create-linkedin-messages',
  standalone: true,
  imports: [NgFor, NgIf, FormsModule,TitleCasePipe],
  templateUrl: './create-linkedin-messages.component.html',
  styleUrl: './create-linkedin-messages.component.scss'
})
export class CreateLinkedinMessagesComponent {
  url = '';
  isLoading = false;
  result: any = null;
  error: string | null = null;
  messageKeyList: string[] = [];

  linkedinProfilePattern = /^https:\/\/(www\.)?linkedin\.com\/in\/[A-Za-z0-9\-_%]+\/?$/;

  constructor(private agentService: ApisService) {}

  objectKeys = Object.keys;

  fetchProfile() {

    if (!this.isValidLinkedinUrl()) {
      this.error = 'Please enter a valid LinkedIn profile URL.';
      return;
    }

    this.isLoading = true;
    this.result = null;
    this.error = null;

    this.agentService.GeneratorLinkedinMessage(this.url).subscribe({
      next: (response) => {
        this.result = response;
        console.log("Response", this.result)
        this.isLoading = false;
      },
      error: (err) => {
        this.error = err.message || 'Something went wrong.';
        this.isLoading = false;
      }
    });
  }

 isValidLinkedinUrl(): boolean {
    return this.linkedinProfilePattern.test(this.url.trim());
  }

 messageKeys(): string[] {
  return Object.keys(this.result || {}).filter(key => key.startsWith('message'));
  }
}
