<div class="container">
  <h1>LinkedIn Profile Scraper</h1>

  <input [(ngModel)]="url" placeholder="Enter LinkedIn profile URL" />
    <button (click)="fetchProfile()" [disabled]="isLoading || !isValidLinkedinUrl()">Scraping</button>

  <div *ngIf="url && !isValidLinkedinUrl()" class="error">
    Please enter a valid LinkedIn profile URL (e.g., https://www.linkedin.com/in/sapreet-gujjar-669297211/).
  </div>

  <div *ngIf="isLoading" class="loader">Loading...</div>
  <div *ngIf="error" class="error">{{ error }}</div>

  <div *ngIf="result?.profileName" class="result-box">
    <h2>{{ result.profileName }}</h2>
    <p>
      <a [href]="result.profileUrl" target="_blank" rel="noopener">
        {{ result.profileUrl }}
      </a>
    </p>

    <h3>Generated Messages</h3>
    <div *ngFor="let key of messageKeys()">
      <div class="message">
        <strong>{{ key | titlecase }}:</strong>
        <p>{{ result[key] }}</p>
      </div>
    </div>
  </div>
</div>
